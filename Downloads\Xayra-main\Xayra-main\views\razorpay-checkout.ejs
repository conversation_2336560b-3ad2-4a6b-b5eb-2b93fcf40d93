<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Razorpay Payment - Bag Shop</title>
    <style>
    body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
    .form-group { margin-bottom: 15px; }
    label { display: block; margin-bottom: 5px; font-weight: bold; }
    input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
    button { background: #3498db; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
    button:hover { background: #2980b9; }
  </style>
  </head>
  <body>
    <h1>Razorpay Payment Gateway</h1>
    <form id="payment-form">
      <div class="form-group">
        <label for="amount">Amount (₹):</label>
        <input type="number" id="amount" name="amount" min="1" required>
      </div>
      <button type="button" onclick="payNow()">Pay Now</button>
    </form>

    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <script>
    async function payNow() {
      const amount = document.getElementById('amount').value;
      
      if (!amount || amount <= 0) {
        showError('Please enter a valid amount');
        return;
      }

      try {
        // Create order by calling the server endpoint
        const response = await fetch('/create-order', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ amount: parseInt(amount) })
        });

        const order = await response.json();

        if (!order.id) {
          showError('Failed to create order');
          return;
        }

        // Open Razorpay Checkout
        const options = {
          key: '<%= process.env.RAZORPAY_KEY_ID || "rzp_test_R6skcQFXQ6yD8C" %>',
          amount: order.amount,
          currency: order.currency,
          name: 'Bag Shop',
          description: 'Purchase from Bag Shop',
          order_id: order.id,
          handler: function (response) {
            // Verify payment
            verifyPayment(response);
          },
          prefill: {
            name: '<%= users ? users.Fullname : "Customer" %>',
            email: '<%= users ? users.Email : "" %>',
            contact: '<%= users ? users.Phone || "" : "" %>'
          },
          theme: {
            color: '#3498db'
          },
          modal: {
            ondismiss: function() {
              // Payment cancelled by user
            }
          }
        };

        const rzp = new Razorpay(options);
        rzp.open();
      } catch (error) {
        showError('Failed to initiate payment');
      }
    }

    async function verifyPayment(response) {
      try {
        const verifyResponse = await fetch('/verify-payment', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            razorpay_order_id: response.razorpay_order_id,
            razorpay_payment_id: response.razorpay_payment_id,
            razorpay_signature: response.razorpay_signature
          })
        });

        const result = await verifyResponse.json();
        
        if (result.status === 'Payment Verified') {
          window.location.href = '/success';
        } else {
          showError('Payment verification failed!');
        }
      } catch (error) {
        showError('Payment verification failed!');
      }
    }

    function showError(message) {
      // Create and show error message
      const errorDiv = document.createElement('div');
      errorDiv.className = 'error-message';
      errorDiv.style.cssText = 'position: fixed; top: 20px; left: 50%; transform: translateX(-50%); background: #e74c3c; color: white; padding: 15px 25px; border-radius: 5px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); z-index: 1000; font-weight: bold;';
      errorDiv.textContent = message;
      document.body.appendChild(errorDiv);
      
      // Auto remove after 4 seconds
      setTimeout(() => {
        errorDiv.remove();
      }, 4000);
    }
  </script>
  </body>
</html>
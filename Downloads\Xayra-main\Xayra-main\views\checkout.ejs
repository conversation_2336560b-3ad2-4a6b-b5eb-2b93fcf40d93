<%- include('./partials/header') %>

<div
  class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8">
  <div class="max-w-6xl mx-auto px-4">
    <div
      class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
      <!-- Checkout Title -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-black text-gray-900 mb-2">Secure Checkout</h1>
        <p class="text-gray-600">Complete your order with confidence</p>
      </div>

      <div class="grid lg:grid-cols-2 gap-8">
        <!-- Left Column: Order Summary -->
        <div
          class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
          <h2 class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <i class="ri-shopping-cart-line mr-3 text-indigo-600"></i>
            Order Summary
          </h2>

          <% if (typeof product !== 'undefined') { %>
          <!-- Single Product Checkout -->
          <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
            <div class="flex items-center gap-4">
              <img
                src="data:image/jpeg;base64,<%= product.image.toString('base64') %>"
                alt="<%= product.name %>"
                class="h-20 w-20 object-contain rounded-lg border-2 border-gray-200" />
              <div class="flex-1">
                <p class="font-bold text-gray-900 text-lg"><%= product.name
                  %></p>
                <div class="flex items-center gap-3 mt-2">
                  <p class="text-xl font-black text-indigo-600">
                    ₹ <%= product.price - (product.price * product.discount /
                    100) %>
                  </p>
                  <% if(product.discount > 0) { %>
                  <p class="text-gray-500 line-through text-sm">₹ <%=
                    product.price %></p>
                  <span
                    class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-semibold">
                    <%= product.discount %>% OFF
                  </span>
                  <% } %>
                </div>
              </div>
            </div>
          </div>
          <% } else if (typeof cart !== 'undefined' && cart.length > 0) { %>
          <!-- Cart Checkout -->
          <div class="space-y-4">
            <% cart.forEach(item => { %>
            <div
              class="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
              <div class="flex items-center gap-4">
                <% if (item.product.image) { %>
                <img
                  src="data:image/jpeg;base64,<%= item.product.image.toString('base64') %>"
                  alt="<%= item.product.name %>"
                  class="h-16 w-16 object-contain rounded-lg border-2 border-gray-200" />
                <% } else { %>
                <div
                  class="h-16 w-16 bg-gray-200 rounded-lg border-2 border-gray-200 flex items-center justify-center">
                  <i class="ri-image-line text-gray-400 text-xl"></i>
                </div>
                <% } %>
                <div class="flex-1">
                  <p class="font-bold text-gray-900"><%= item.product.name
                    %></p>
                  <p class="text-sm text-gray-600 flex items-center mt-1">
                    <i class="ri-stack-line mr-1"></i>
                    Quantity: <span class="font-semibold ml-1"><%= item.quantity
                      %></span>
                  </p>
                  <div class="flex items-center gap-3 mt-2">
                    <p class="text-lg font-black text-indigo-600">
                      ₹ <%= (item.product.price - (item.product.price *
                      (item.product.discount || 0) / 100)) * item.quantity %>
                    </p>
                    <% if(item.product.discount > 0) { %>
                    <p class="text-sm text-gray-500 line-through">₹ <%=
                      item.product.price * item.quantity %></p>
                    <span
                      class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-semibold">
                      <%= item.product.discount %>% OFF
                    </span>
                    <% } %>
                  </div>
                </div>
              </div>
            </div>
            <% }) %>

            <!-- Total -->
            <div
              class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg p-4 mt-6">
              <div class="flex justify-between items-center text-white">
                <span class="text-lg font-bold flex items-center">
                  <i class="ri-money-rupee-circle-line mr-2"></i>
                  Total Amount:
                </span>
                <span class="text-2xl font-black">
                  ₹ <%
                  let total = 0;
                  cart.forEach(item => {
                  total += (item.product.price - (item.product.price *
                  (item.product.discount || 0) / 100)) * item.quantity;
                  });
                  %><%= total %>
                </span>
              </div>
            </div>
          </div>
          <% } %>
        </div>

        <!-- Right Column: Delivery & Payment -->
        <div class="space-y-6">
          <form
            action="<%= typeof product !== 'undefined' ? '/order/confirm/' + product._id : '/order/confirm-cart' %>"
            method="POST" class="space-y-6">
            <input type="hidden" name="paymentStatus" value="Pending" />

            <!-- Delivery Address -->
            <div
              class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
              <h2
                class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="ri-map-pin-line mr-3 text-green-600"></i>
                Delivery Address
              </h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <input type="text" name="fullName" placeholder="Full Name"
                  required
                  class="w-full border-2 border-gray-200 rounded-xl p-4 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all bg-white" />
                <input type="text" name="phone" placeholder="Phone Number"
                  required
                  class="w-full border-2 border-gray-200 rounded-xl p-4 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all bg-white" />
                <input type="text" name="address" placeholder="Street Address"
                  required
                  class="w-full border-2 border-gray-200 rounded-xl p-4 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all bg-white col-span-2" />
                <input type="text" name="city" placeholder="City" required
                  class="w-full border-2 border-gray-200 rounded-xl p-4 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all bg-white" />
                <input type="text" name="pincode" placeholder="Pincode" required
                  class="w-full border-2 border-gray-200 rounded-xl p-4 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all bg-white" />
              </div>

              <!-- Delivery Time -->
              <div class="col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  <i class="ri-time-line mr-2"></i>Preferred Delivery Time
                </label>
                <select name="deliveryTime"
                  class="w-full border-2 border-gray-200 rounded-xl p-4 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all bg-white">
                  <option value="anytime">Anytime (9 AM - 9 PM)</option>
                  <option value="morning">Morning (9 AM - 12 PM)</option>
                  <option value="afternoon">Afternoon (12 PM - 4 PM)</option>
                  <option value="evening">Evening (4 PM - 9 PM)</option>
                </select>
              </div>
            </div>

            <!-- Payment Options -->
            <div
              class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
              <h2
                class="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                <i class="ri-bank-card-line mr-3 text-purple-600"></i>
                Payment Method
              </h2>
              <div class="space-y-4">
                <label
                  class="flex items-center gap-4 p-4 bg-white rounded-lg border-2 border-gray-200 hover:border-indigo-300 cursor-pointer transition-all">
                  <input type="radio" name="paymentMethod" value="cod" checked
                    class="w-5 h-5 text-indigo-600" />
                  <div class="flex items-center gap-3">
                    <i class="ri-hand-coin-line text-2xl text-green-600"></i>
                    <div>
                      <span class="font-semibold text-gray-900">Cash on
                        Delivery</span>
                      <p class="text-sm text-gray-600">Pay when you receive your
                        order</p>
                    </div>
                  </div>
                </label>
                <label
                  class="flex items-center gap-4 p-4 bg-white rounded-lg border-2 border-gray-200 hover:border-indigo-300 cursor-pointer transition-all">
                  <input type="radio" name="paymentMethod" value="card"
                    class="w-5 h-5 text-indigo-600" />
                  <div class="flex items-center gap-3">
                    <i class="ri-bank-card-line text-2xl text-blue-600"></i>
                    <div>
                      <span class="font-semibold text-gray-900">Razorpay</span>
                      <p class="text-sm text-gray-600">UPI, Cards, NetBanking &
                        Wallets</p>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            <!-- Confirm Order -->
            <button type="button" onclick="handleOrder()"
              class="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-4 rounded-xl font-bold text-lg hover:from-green-700 hover:to-emerald-700 transition-all shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center justify-center gap-3">
              <i class="ri-check-double-line text-xl"></i>
              Confirm Order
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script>
function handleOrder() {
  const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked').value;
  const form = document.querySelector('form');

  // Validate form fields first
  const fullName = document.querySelector('input[name="fullName"]').value.trim();
  const phone = document.querySelector('input[name="phone"]').value.trim();
  const address = document.querySelector('input[name="address"]').value.trim();
  const city = document.querySelector('input[name="city"]').value.trim();
  const pincode = document.querySelector('input[name="pincode"]').value.trim();

  if (!fullName || !phone || !address || !city || !pincode) {
    showError('Please fill all delivery details');
    return;
  }

  // Validate phone number (basic validation)
  if (!/^\d{10}$/.test(phone)) {
    showError('Please enter a valid 10-digit phone number');
    return;
  }

  // Validate pincode (basic validation)
  if (!/^\d{6}$/.test(pincode)) {
    showError('Please enter a valid 6-digit pincode');
    return;
  }

  if (paymentMethod === 'cod') {
    // Show loading state
    const submitBtn = document.querySelector('button[onclick="handleOrder()"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="ri-loader-4-line animate-spin text-xl"></i> Processing Order...';
    submitBtn.disabled = true;

    // Submit form normally for COD
    try {
      form.submit();
    } catch (error) {
      showError('Failed to submit order. Please try again.');
      submitBtn.innerHTML = originalText;
      submitBtn.disabled = false;
    }
  } else if (paymentMethod === 'card') {
    // Handle Razorpay payment - validation already done above
    const deliveryTime = document.querySelector('select[name="deliveryTime"]').value;
    
    // Calculate total amount
    <% if (typeof product !== 'undefined') { %>
      const amount = <%= product.price - (product.price * product.discount / 100) %>;
    <% } else if (typeof cart !== 'undefined' && cart.length > 0) { %>
      const amount = <% 
        let total = 0;
        cart.forEach(item => {
          total += (item.product.price - (item.product.price * (item.product.discount || 0) / 100)) * item.quantity;
        });
      %><%= total %>;
    <% } else { %>
      const amount = 0;
    <% } %>
    
    initiateRazorpayPayment(amount, { fullName, phone, address, city, pincode, deliveryTime });
  }
}

async function initiateRazorpayPayment(amount, deliveryDetails) {
  try {
    // Create order
    const response = await fetch('/create-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ amount: amount })
    });

    const order = await response.json();

    if (!order.id) {
      showError('Failed to create order');
      return;
    }

    // Open Razorpay Checkout
    const options = {
      key: '<%= process.env.RAZORPAY_KEY_ID || "rzp_test_R6skcQFXQ6yD8C" %>',
      amount: order.amount,
      currency: order.currency,
      name: 'Bag Shop',
      description: 'Purchase from Bag Shop',
      order_id: order.id,
      handler: function (response) {
        verifyPayment(response, deliveryDetails);
      },
      prefill: {
        name: deliveryDetails.fullName,
        email: '<%= users ? users.Email : "" %>',
        contact: deliveryDetails.phone
      },
      theme: {
        color: '#3498db'
      },
      modal: {
        ondismiss: function() {
          // Payment cancelled by user
        }
      }
    };

    const rzp = new Razorpay(options);
    rzp.open();
  } catch (error) {
    showError('Failed to initiate payment');
  }
}

async function verifyPayment(response, deliveryDetails) {
  try {
    const verifyResponse = await fetch('/verify-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        razorpay_order_id: response.razorpay_order_id,
        razorpay_payment_id: response.razorpay_payment_id,
        razorpay_signature: response.razorpay_signature,
        orderData: deliveryDetails
      })
    });

    const result = await verifyResponse.json();
    
    if (result.status === 'Payment Verified') {
      window.location.href = result.redirect || '/success';
    } else {
      showError('Payment verification failed!');
    }
  } catch (error) {
    showError('Payment verification failed!');
  }
}

function showError(message) {
  // Create and show error message
  const errorDiv = document.createElement('div');
  errorDiv.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
  errorDiv.textContent = message;
  document.body.appendChild(errorDiv);
  
  // Auto remove after 4 seconds
  setTimeout(() => {
    errorDiv.remove();
  }, 4000);
}
</script>

<%- include('./partials/footer') %>
